#!/bin/bash

# Deploy City Insights Data Import Job to Cloud Run
# This script builds and deploys the data import job manually

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PROJECT_ID is set
if [ -z "$GOOGLE_CLOUD_PROJECT_ID" ]; then
    print_error "GOOGLE_CLOUD_PROJECT_ID environment variable is not set"
    print_status "Please set it with: export GOOGLE_CLOUD_PROJECT_ID=your-project-id"
    exit 1
fi

PROJECT_ID=$GOOGLE_CLOUD_PROJECT_ID
REGION=${REGION:-us-central1}
IMAGE_NAME="gcr.io/$PROJECT_ID/city-insights-data-import"

print_status "🏙️  Deploying City Insights Data Import Job"
print_status "Project ID: $PROJECT_ID"
print_status "Region: $REGION"
print_status "Image: $IMAGE_NAME"

# Build the Docker image
print_status "🔨 Building Docker image..."
cd backend
docker build -f Dockerfile.job -t $IMAGE_NAME:latest .
cd ..

# Push the image to Google Container Registry
print_status "📤 Pushing image to Google Container Registry..."
docker push $IMAGE_NAME:latest

# Deploy the job (create or update)
print_status "🚀 Deploying Cloud Run job..."

# Try to update first, if it fails, create it
if ! gcloud run jobs update city-insights-data-import \
  --image $IMAGE_NAME:latest \
  --region=$REGION \
  --task-timeout 3600 \
  --parallelism 1 \
  --task-count 1 \
  --max-retries 3 \
  --memory 4Gi \
  --cpu 2 \
  --set-env-vars NODE_ENV=production,GOOGLE_CLOUD_PROJECT_ID=$PROJECT_ID \
  --set-secrets MONGODB_URI=mongodb-uri:latest,MONGODB_DB_NAME=mongodb-db-name:latest,GEMINI_API_KEY=gemini-api-key:latest 2>/dev/null; then

  print_status "Job doesn't exist, creating new job..."
  gcloud run jobs create city-insights-data-import \
    --image $IMAGE_NAME:latest \
    --region=$REGION \
    --task-timeout 3600 \
    --parallelism 1 \
    --task-count 1 \
    --max-retries 3 \
    --memory 4Gi \
    --cpu 2 \
    --set-env-vars NODE_ENV=production,GOOGLE_CLOUD_PROJECT_ID=$PROJECT_ID \
    --set-secrets MONGODB_URI=mongodb-uri:latest,MONGODB_DB_NAME=mongodb-db-name:latest,GEMINI_API_KEY=gemini-api-key:latest
else
  print_status "Job updated successfully!"
fi

print_success "✅ Data import job deployed successfully!"
print_status "You can now execute the job with:"
print_status "gcloud run jobs execute city-insights-data-import --region=$REGION --wait"
