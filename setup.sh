#!/bin/bash

# City Insights AI - Setup Script
# This script sets up the development environment for the City Insights AI project

set -e

echo "🏙️  City Insights AI - Setup Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is 18 or higher
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_warning "Node.js version 18 or higher is recommended. Current: $NODE_VERSION"
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker is installed: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. Install Docker to use containerized development."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
    
    print_success "All dependencies installed successfully!"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Root .env file
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created root .env file"
    else
        print_warning "Root .env file already exists"
    fi
    
    # Backend .env file
    if [ ! -f backend/.env ]; then
        cp backend/.env.example backend/.env
        print_success "Created backend .env file"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Frontend .env file
    if [ ! -f frontend/.env ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend .env file"
    else
        print_warning "Frontend .env file already exists"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p backend/logs
    mkdir -p data/raw
    mkdir -p data/processed
    mkdir -p credentials
    
    print_success "Directories created successfully!"
}

# Setup Google Cloud credentials placeholder
setup_credentials() {
    print_status "Setting up credentials..."
    
    if [ ! -f credentials/service-account.json ]; then
        cat > credentials/service-account.json << EOF
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
        print_warning "Created placeholder service account credentials. Please replace with your actual Google Cloud service account key."
    else
        print_warning "Service account credentials already exist"
    fi
}

# Display next steps
show_next_steps() {
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "=============="
    echo ""
    echo "1. 🔧 Configure your environment variables:"
    echo "   - Edit .env files with your actual values"
    echo "   - Set up MongoDB Atlas connection string"
    echo "   - Add Google Cloud Project ID and credentials"
    echo "   - Add Google Maps API key"
    echo ""
    echo "2. 🗄️  Set up MongoDB:"
    echo "   - Create a MongoDB Atlas cluster (recommended)"
    echo "   - Or use local MongoDB with Docker: docker-compose --profile local up mongodb"
    echo ""
    echo "3. 🤖 Configure Google Cloud:"
    echo "   - Enable Vertex AI API in your Google Cloud project"
    echo "   - Create a service account with Vertex AI permissions"
    echo "   - Download the service account key and place it in credentials/"
    echo ""
    echo "4. 🗺️  Set up Google Maps:"
    echo "   - Enable Maps JavaScript API in Google Cloud Console"
    echo "   - Create an API key and add it to your .env files"
    echo ""
    echo "5. 📊 Import sample data:"
    echo "   - Run: npm run data:import"
    echo "   - Set up vector search index: npm run setup:vector"
    echo ""
    echo "6. 🚀 Start the development servers:"
    echo "   - Run: npm run dev"
    echo "   - Backend will be available at http://localhost:8080"
    echo "   - Frontend will be available at http://localhost:5173"
    echo ""
    echo "7. 🐳 Alternative - Use Docker:"
    echo "   - Run: docker-compose up"
    echo "   - This will start all services in containers"
    echo ""
    echo "📚 Documentation:"
    echo "   - Check README.md for detailed setup instructions"
    echo "   - API documentation available at http://localhost:8080/health"
    echo ""
    echo "🆘 Need help?"
    echo "   - Check the troubleshooting section in README.md"
    echo "   - Ensure all environment variables are properly set"
    echo ""
}

# Main setup function
main() {
    echo ""
    print_status "Starting City Insights AI setup..."
    echo ""
    
    check_node
    check_docker
    echo ""
    
    install_dependencies
    echo ""
    
    setup_env_files
    echo ""
    
    create_directories
    echo ""
    
    setup_credentials
    echo ""
    
    show_next_steps
}

# Run main function
main
