apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: city-insights-data-import
  annotations:
    run.googleapis.com/launch-stage: BETA
spec:
  template:
    spec:
      taskCount: 1
      parallelism: 1
      taskTimeoutSeconds: 3600
      restartPolicy: OnFailure
      template:
        spec:
          containers:
          - image: gcr.io/${PROJECT_ID}/city-insights-data-import:latest
            env:
            - name: NODE_ENV
              value: "production"
            - name: GOOGLE_CLOUD_PROJECT_ID
              value: "${PROJECT_ID}"
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: mongodb-uri
                  key: latest
            - name: MONGODB_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: mongodb-db-name
                  key: latest
            - name: GEMINI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: gemini-api-key
                  key: latest
            resources:
              limits:
                cpu: "2"
                memory: "4Gi"
