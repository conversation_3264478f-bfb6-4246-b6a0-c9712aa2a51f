import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Paper
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  AttachMoney as MoneyIcon,
  Home as HomeIcon
} from '@mui/icons-material'
import { Helmet } from 'react-helmet-async'
import { useQuery } from 'react-query'
import ReactMarkdown from 'react-markdown'
import { neighborhoodAPI } from '../services/api'

const MarketInsights = () => {

  // Fetch market insights
  const { data: marketData, isLoading, error } = useQuery(
    'market-insights',
    neighborhoodAPI.getMarketInsights,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  )

  const insights = marketData?.data?.insights
  const dataPoints = marketData?.data?.dataPoints

  const formatCurrency = (amount) => {
    return `R${amount?.toLocaleString() || 'N/A'}`
  }

  const getTrendIcon = (type) => {
    switch (type) {
      case 'hot':
        return <TrendingUpIcon color="error" />
      case 'value':
        return <MoneyIcon color="success" />
      case 'luxury':
        return <StarIcon color="warning" />
      default:
        return <HomeIcon color="primary" />
    }
  }

  const getTrendColor = (type) => {
    switch (type) {
      case 'hot':
        return 'error'
      case 'value':
        return 'success'
      case 'luxury':
        return 'warning'
      default:
        return 'primary'
    }
  }

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load market insights. Please try again later.
        </Alert>
      </Box>
    )
  }

  return (
    <>
      <Helmet>
        <title>Market Insights - City Insights AI</title>
        <meta name="description" content="AI-powered Cape Town real estate market insights and trends" />
      </Helmet>

      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight={600}>
          🏠 Cape Town Market Insights
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          AI-powered analysis of Cape Town's rental market trends and opportunities
        </Typography>

        <Grid container spacing={3}>
          {/* Market Overview Cards */}
          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.05) 100%)',
              border: 1,
              borderColor: 'primary.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="primary" sx={{ fontWeight: 600, mb: 2 }}>
                  💰 Average Rent
                </Typography>
                <Typography variant="h3" fontWeight={700} color="primary.main" sx={{ mb: 1 }}>
                  {formatCurrency(insights?.marketData?.averageRent)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Across {dataPoints || 0} neighborhoods
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%)',
              border: 1,
              borderColor: 'success.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="success.main" sx={{ fontWeight: 600, mb: 2 }}>
                  🛡️ Average Safety
                </Typography>
                <Typography variant="h3" fontWeight={700} color="success.main" sx={{ mb: 1 }}>
                  {insights?.marketData?.averageSafety || 'N/A'}/10
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  City-wide safety score
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 152, 0, 0.05) 100%)',
              border: 1,
              borderColor: 'warning.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="warning.main" sx={{ fontWeight: 600, mb: 2 }}>
                  📊 Price Range
                </Typography>
                <Typography variant="h5" fontWeight={700} color="warning.main" sx={{ mb: 1 }}>
                  {formatCurrency(insights?.marketData?.rentRange?.min)} - {formatCurrency(insights?.marketData?.rentRange?.max)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Market spread
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Trending Insights */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 152, 0, 0.02) 100%)',
              border: 1,
              borderColor: 'warning.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'warning.main',
                  fontWeight: 600
                }}>
                  🔥 Trending Now
                </Typography>
                {insights?.trends?.map((trend, index) => (
                  <Paper
                    key={index}
                    sx={{
                      mb: 2,
                      p: 2.5,
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-1px)',
                        boxShadow: 2
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                      {getTrendIcon(trend.type)}
                      <Typography variant="subtitle2" sx={{ ml: 1, fontWeight: 600, color: 'text.primary' }}>
                        {trend.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5, lineHeight: 1.5 }}>
                      {trend.description}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.75 }}>
                      {trend.neighborhoods?.map((neighborhood, idx) => (
                        <Chip
                          key={idx}
                          label={neighborhood}
                          size="small"
                          color={getTrendColor(trend.type)}
                          variant="outlined"
                          sx={{
                            fontWeight: 500,
                            '&:hover': {
                              backgroundColor: `${getTrendColor(trend.type)}.50`
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </Paper>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Market Recommendations */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%)',
              border: 1,
              borderColor: 'success.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'success.main',
                  fontWeight: 600
                }}>
                  💡 AI Recommendations
                </Typography>
                {insights?.recommendations?.map((rec, index) => (
                  <Paper
                    key={index}
                    sx={{
                      mb: 2,
                      p: 2.5,
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-1px)',
                        boxShadow: 2
                      }
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} color="success.dark" sx={{ mb: 1 }}>
                      {rec.category}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1.5, lineHeight: 1.5 }}>
                      {rec.recommendation}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{
                      display: 'block',
                      padding: 1,
                      backgroundColor: 'grey.50',
                      borderRadius: 1,
                      fontStyle: 'italic'
                    }}>
                      Budget: {rec.budget} • {rec.reasoning}
                    </Typography>
                  </Paper>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Detailed AI Analysis */}
          <Grid item xs={12}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(25, 118, 210, 0.02) 100%)',
              border: 1,
              borderColor: 'primary.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'primary.main',
                  fontWeight: 600
                }}>
                  🤖 Detailed AI Analysis
                </Typography>
                <Paper sx={{
                  p: 3,
                  backgroundColor: 'rgba(255,255,255,0.95)',
                  color: 'text.primary',
                  borderRadius: 2,
                  border: 1,
                  borderColor: 'divider'
                }}>
                  <Box
                    sx={{
                      '& h1, & h2, & h3, & h4, & h5, & h6': {
                        color: 'primary.main',
                        fontWeight: 600,
                        margin: 0,
                        marginBottom: 1.5,
                        '&:first-of-type': {
                          marginTop: 0
                        }
                      },
                      '& strong': {
                        fontWeight: 600,
                        color: 'primary.main'
                      },
                      '& ul, & ol': {
                        paddingLeft: 2,
                        margin: 0,
                        marginBottom: 1.5
                      },
                      '& li': {
                        marginBottom: 0.5,
                        lineHeight: 1.6
                      },
                      '& p': {
                        margin: 0,
                        marginBottom: 1.5,
                        lineHeight: 1.6,
                        '&:last-child': {
                          marginBottom: 0
                        }
                      },
                      '& em': {
                        fontStyle: 'italic',
                        color: 'text.secondary'
                      }
                    }}
                  >
                    <ReactMarkdown>
                      {insights?.insights || 'No detailed analysis available.'}
                    </ReactMarkdown>
                  </Box>
                </Paper>
              </CardContent>
            </Card>
          </Grid>

          {/* Borough Statistics */}
          {insights?.marketData?.boroughStats && (
            <Grid item xs={12}>
              <Card sx={{
                background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(156, 39, 176, 0.02) 100%)',
                border: 1,
                borderColor: 'secondary.light'
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    color: 'secondary.main',
                    fontWeight: 600,
                    mb: 3
                  }}>
                    📊 Borough Breakdown
                  </Typography>
                  <Grid container spacing={3}>
                    {insights.marketData.boroughStats.slice(0, 6).map((borough, index) => (
                      <Grid item xs={12} sm={6} md={4} key={index}>
                        <Paper sx={{
                          p: 2.5,
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 2,
                          backgroundColor: 'rgba(255,255,255,0.9)',
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 2,
                            borderColor: 'secondary.main'
                          }
                        }}>
                          <Typography variant="subtitle1" fontWeight={600} color="secondary.main" sx={{ mb: 1.5 }}>
                            {borough.borough}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            📍 {borough.count} neighborhoods
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                            💰 Avg Rent: {formatCurrency(borough.avgRent)}
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            🛡️ Safety: {borough.avgSafety}/10
                          </Typography>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        <Paper sx={{
          mt: 4,
          p: 3,
          backgroundColor: 'rgba(25, 118, 210, 0.05)',
          borderRadius: 2,
          border: 1,
          borderColor: 'primary.light',
          textAlign: 'center'
        }}>
          <Typography variant="body2" color="text.secondary" sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            flexWrap: 'wrap'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              🕒 Last updated: {new Date(marketData?.data?.lastUpdated || Date.now()).toLocaleString()}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              📊 Data points: {dataPoints || 0} neighborhoods
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              🤖 Powered by Gemini AI
            </Box>
          </Typography>
        </Paper>
      </Box>
    </>
  )
}

export default MarketInsights
