{"_message":"Neighborhood validation failed","errors":{"vectorEmbedding":{"kind":"user defined","message":"Vector embedding must have exactly 768 dimensions","name":"ValidatorError","path":"vectorEmbedding","properties":{"message":"Vector embedding must have exactly 768 dimensions","path":"vectorEmbedding","type":"user defined","value":[]},"value":[]}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError importing neighborhoods: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\u001b[39m","stack":"ValidationError: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 14:26:54:2654"}
{"_message":"Neighborhood validation failed","errors":{"vectorEmbedding":{"kind":"user defined","message":"Vector embedding must have exactly 768 dimensions","name":"ValidatorError","path":"vectorEmbedding","properties":{"message":"Vector embedding must have exactly 768 dimensions","path":"vectorEmbedding","type":"user defined","value":[]},"value":[]}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError importing neighborhoods: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\u001b[39m","stack":"ValidationError: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 14:28:36:2836"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for SoHo: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for Greenwich Village: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for Williamsburg: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for Park Slope: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for Astoria: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: this.embeddingModel.predict is not a function\u001b[39m","stack":"TypeError: this.embeddingModel.predict is not a function\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:25:52)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating neighborhood embedding: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to generate embedding for Upper East Side: Failed to generate embedding\u001b[39m","stack":"Error: Failed to generate embedding\n    at GeminiService.generateEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:29:13)\n    at GeminiService.generateNeighborhoodEmbedding (/home/<USER>/aiinaction/backend/src/services/geminiService.js:42:25)\n    at importNeighborhoods (/home/<USER>/aiinaction/backend/scripts/dataImport.js:275:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async main (/home/<USER>/aiinaction/backend/scripts/dataImport.js:372:5)","timestamp":"2025-06-05 14:29:52:2952"}
{"code":"ENOENT","errno":-2,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: The file at path/to/service-account-key.json does not exist, or it is not a file. ENOENT: no such file or directory, lstat '/home/<USER>/aiinaction/backend/path'\u001b[39m","path":"/home/<USER>/aiinaction/backend/path","stack":"Error: The file at path/to/service-account-key.json does not exist, or it is not a file. ENOENT: no such file or directory, lstat '/home/<USER>/aiinaction/backend/path'\n    at Object.realpathSync (node:fs:2707:29)\n    at GoogleAuth._getApplicationCredentialsFromFilePath (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:380:27)\n    at GoogleAuth._tryGetApplicationCredentialsFromEnvironmentVariable (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:321:25)\n    at GoogleAuth.getApplicationDefaultAsync (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:257:24)\n    at #determineClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:732:47)\n    at GoogleAuth.getClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:707:61)\n    at GrpcClient._getCredentials (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:186:40)\n    at GrpcClient.createStub (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:367:34)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"lstat","timestamp":"2025-06-05 14:34:28:3428"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\u001b[39m","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:284:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async #determineClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:732:36)\n    at async GoogleAuth.getClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:709:20)\n    at async GrpcClient._getCredentials (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:186:24)\n    at async GrpcClient.createStub (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:367:23)","timestamp":"2025-06-05 14:35:11:3511"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating embedding: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\u001b[39m","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:284:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async #determineClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:732:36)\n    at async GoogleAuth.getClient (/home/<USER>/aiinaction/node_modules/google-gax/node_modules/google-auth-library/build/src/auth/googleauth.js:709:20)\n    at async GrpcClient._getCredentials (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:186:24)\n    at async GrpcClient.createStub (/home/<USER>/aiinaction/node_modules/google-gax/build/src/grpc.js:367:23)","timestamp":"2025-06-05 14:35:27:3527"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async /home/<USER>/aiinaction/backend/src/routes/analytics.js:122:22","stackTrace":{},"timestamp":"2025-06-05 14:47:58:4758"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","stackTrace":{},"timestamp":"2025-06-05 14:47:58:4758"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/analytics.js:122:22","timestamp":"2025-06-05 14:47:58:4758"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 14:47:58:4758"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 14:47:58:4758"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async /home/<USER>/aiinaction/backend/src/routes/analytics.js:122:22","stackTrace":{},"timestamp":"2025-06-05 14:47:59:4759"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/analytics.js:122:22","timestamp":"2025-06-05 14:47:59:4759"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","stackTrace":{},"timestamp":"2025-06-05 14:47:59:4759"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 14:47:59:4759"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:197:34)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 14:47:59:4759"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:71:18","stackTrace":{},"timestamp":"2025-06-05 14:51:26:5126"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError processing chat message: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:71:18","timestamp":"2025-06-05 14:51:26:5126"}
{"cause":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [VertexAI.GoogleAuthError]: \u001b[39m\n\u001b[31mUnable to authenticate your request        \u001b[39m\n\u001b[31mDepending on your run time environment, you can get authentication by        \u001b[39m\n\u001b[31m- if in local instance or cloud shell: `!gcloud auth login`        \u001b[39m\n\u001b[31m- if in Colab:        \u001b[39m\n\u001b[31m    -`from google.colab import auth`        \u001b[39m\n\u001b[31m    -`auth.authenticate_user()`        \u001b[39m\n\u001b[31m- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\u001b[39m","name":"GoogleAuthError","stack":"GoogleAuthError: [VertexAI.GoogleAuthError]: \nUnable to authenticate your request        \nDepending on your run time environment, you can get authentication by        \n- if in local instance or cloud shell: `!gcloud auth login`        \n- if in Colab:        \n    -`from google.colab import auth`        \n    -`auth.authenticate_user()`        \n- if in service account or other: please follow guidance in https://cloud.google.com/docs/authentication\n    at /home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/models/generative_models.js:63:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google-cloud/vertexai/build/src/functions/generate_content.js:51:16)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:209:22)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:53:20","stackTrace":{},"timestamp":"2025-06-05 14:51:45:5145"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError processing chat message: Failed to generate AI response\u001b[39m","stack":"Error: Failed to generate AI response\n    at GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:214:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:53:20","timestamp":"2025-06-05 14:51:45:5145"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating chat response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\u001b[39m","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (/home/<USER>/aiinaction/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async makeRequest (/home/<USER>/aiinaction/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/home/<USER>/aiinaction/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async GeminiService.generateChatResponse (/home/<USER>/aiinaction/backend/src/services/geminiService.js:240:22)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:73:18","status":400,"statusText":"Bad Request","timestamp":"2025-06-05 20:18:56:1856"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError Expected ':' after property name in JSON at position 19\u001b[39m","timestamp":"2025-06-05 20:19:22:1922"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`4` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`4` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"4"},"value":"4"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Camps Bay: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`2` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`2` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"2"},"value":"2"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Sea Point: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `2` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `2` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`4` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`4` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"4"},"value":"4"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Rondebosch: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`2` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`2` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"2"},"value":"2"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Mitchells Plain: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `2` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `2` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`3` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`3` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"3"},"value":"3"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Khayelitsha: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `3` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `3` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`4` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`4` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"4"},"value":"4"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Cape Town Central: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `4` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`3` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`3` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"3"},"value":"3"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Bellville: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `3` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `3` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"_message":"CrimeData validation failed","errors":{"borough":{"kind":"required","message":"Path `borough` is required.","name":"ValidatorError","path":"borough","properties":{"message":"Path `borough` is required.","path":"borough","type":"required"}},"category":{"kind":"required","message":"Path `category` is required.","name":"ValidatorError","path":"category","properties":{"message":"Path `category` is required.","path":"category","type":"required"}},"coordinates.lat":{"kind":"required","message":"Path `coordinates.lat` is required.","name":"ValidatorError","path":"coordinates.lat","properties":{"message":"Path `coordinates.lat` is required.","path":"coordinates.lat","type":"required"}},"coordinates.lng":{"kind":"required","message":"Path `coordinates.lng` is required.","name":"ValidatorError","path":"coordinates.lng","properties":{"message":"Path `coordinates.lng` is required.","path":"coordinates.lng","type":"required"}},"dayOfWeek":{"kind":"required","message":"Path `dayOfWeek` is required.","name":"ValidatorError","path":"dayOfWeek","properties":{"message":"Path `dayOfWeek` is required.","path":"dayOfWeek","type":"required"}},"incidentType":{"kind":"required","message":"Path `incidentType` is required.","name":"ValidatorError","path":"incidentType","properties":{"message":"Path `incidentType` is required.","path":"incidentType","type":"required"}},"month":{"kind":"required","message":"Path `month` is required.","name":"ValidatorError","path":"month","properties":{"message":"Path `month` is required.","path":"month","type":"required"}},"neighborhood":{"kind":"required","message":"Path `neighborhood` is required.","name":"ValidatorError","path":"neighborhood","properties":{"message":"Path `neighborhood` is required.","path":"neighborhood","type":"required"}},"severity":{"kind":"enum","message":"`1` is not a valid enum value for path `severity`.","name":"ValidatorError","path":"severity","properties":{"enumValues":["LOW","MEDIUM","HIGH"],"message":"`1` is not a valid enum value for path `severity`.","path":"severity","type":"enum","value":"1"},"value":"1"},"timeOfDay":{"kind":"required","message":"Path `timeOfDay` is required.","name":"ValidatorError","path":"timeOfDay","properties":{"message":"Path `timeOfDay` is required.","path":"timeOfDay","type":"required"}},"year":{"kind":"required","message":"Path `year` is required.","name":"ValidatorError","path":"year","properties":{"message":"Path `year` is required.","path":"year","type":"required"}}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Stellenbosch: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `1` is not a valid enum value for path `severity`.\u001b[39m","stack":"ValidationError: CrimeData validation failed: year: Path `year` is required., month: Path `month` is required., dayOfWeek: Path `dayOfWeek` is required., timeOfDay: Path `timeOfDay` is required., coordinates.lng: Path `coordinates.lng` is required., coordinates.lat: Path `coordinates.lat` is required., category: Path `category` is required., incidentType: Path `incidentType` is required., borough: Path `borough` is required., neighborhood: Path `neighborhood` is required., severity: `1` is not a valid enum value for path `severity`.\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-05 21:49:19:4919"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: At least 2 neighborhoods required for comparison\u001b[39m","stack":"Error: At least 2 neighborhoods required for comparison\n    at VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:273:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 21:56:07:567"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: At least 2 neighborhoods required for comparison\u001b[39m","stack":"Error: At least 2 neighborhoods required for comparison\n    at VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:273:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 21:56:07:567"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: At least 2 neighborhoods required for comparison\u001b[39m","stack":"Error: At least 2 neighborhoods required for comparison\n    at VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:273:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 21:56:08:568"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError comparing neighborhoods: At least 2 neighborhoods required for comparison\u001b[39m","stack":"Error: At least 2 neighborhoods required for comparison\n    at VectorSearchService.compareNeighborhoods (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:273:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:208:24","timestamp":"2025-06-05 21:56:08:568"}
{"_message":"Neighborhood validation failed","errors":{"vectorEmbedding":{"kind":"user defined","message":"Vector embedding must have exactly 768 dimensions","name":"ValidatorError","path":"vectorEmbedding","properties":{"message":"Vector embedding must have exactly 768 dimensions","path":"vectorEmbedding","type":"user defined","value":[]},"value":[]}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Quick setup failed: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\u001b[39m","stack":"ValidationError: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-06 10:53:21:5321"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching data from https://data.cityofnewyork.us/resource/hg8x-zxpr.json:\u001b[39m","timestamp":"2025-06-06 10:53:25:5325"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching data from https://data.cityofnewyork.us/resource/5uac-w243.json:\u001b[39m","timestamp":"2025-06-06 10:53:25:5325"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching data from https://data.cityofnewyork.us/resource/erm2-nwe9.json:\u001b[39m","timestamp":"2025-06-06 10:53:25:5325"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching data from https://data.cityofnewyork.us/resource/kku6-nxdu.json:\u001b[39m","timestamp":"2025-06-06 10:53:26:5326"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: test.neighborhoods index: name_1_borough_1 dup key: { name: \"Cape Town Central\", borough: \"City Bowl\" }","index":0,"keyPattern":{"borough":1,"name":1},"keyValue":{"borough":"City Bowl","name":"Cape Town Central"}},"index":0,"keyPattern":{"borough":1,"name":1},"keyValue":{"borough":"City Bowl","name":"Cape Town Central"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error importing Cape Town Central: E11000 duplicate key error collection: test.neighborhoods index: name_1_borough_1 dup key: { name: \"Cape Town Central\", borough: \"City Bowl\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: test.neighborhoods index: name_1_borough_1 dup key: { name: \"Cape Town Central\", borough: \"City Bowl\" }\n    at InsertOneOperation.execute (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-06 11:39:32:3932"}
{"_message":"Neighborhood validation failed","errors":{"vectorEmbedding":{"kind":"user defined","message":"Vector embedding must have exactly 768 dimensions","name":"ValidatorError","path":"vectorEmbedding","properties":{"message":"Vector embedding must have exactly 768 dimensions","path":"vectorEmbedding","type":"user defined","value":[]},"value":[]}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError adding Kenilworth: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\u001b[39m","stack":"ValidationError: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-06 14:58:59:5859"}
{"_message":"Neighborhood validation failed","errors":{"vectorEmbedding":{"kind":"user defined","message":"Vector embedding must have exactly 768 dimensions","name":"ValidatorError","path":"vectorEmbedding","properties":{"message":"Vector embedding must have exactly 768 dimensions","path":"vectorEmbedding","type":"user defined","value":[]},"value":[]}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError adding Kenilworth: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\u001b[39m","stack":"ValidationError: Neighborhood validation failed: vectorEmbedding: Vector embedding must have exactly 768 dimensions\n    at Document.invalidate (/home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/aiinaction/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-06 14:59:03:593"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l3p1o7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1243441,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l3p1o7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"stack":"MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/\n    at _handleConnectionErrors (/home/<USER>/aiinaction/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/aiinaction/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDB (/home/<USER>/aiinaction/backend/src/config/database.js:20:18)\n    at async startServer (/home/<USER>/aiinaction/backend/src/server.js:95:5)","timestamp":"2025-06-08 00:27:34:2734"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":30,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017":{"$clusterTime":{"clusterTime":{"$timestamp":{"i":3,"t":**********}},"signature":{"hash":{"0":44,"1":45,"10":208,"11":121,"12":254,"13":94,"14":196,"15":146,"16":99,"17":156,"18":111,"19":196,"2":208,"3":42,"4":248,"5":76,"6":74,"7":113,"8":68,"9":172},"keyId":7464295174923878000}},"address":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":["ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017"],"iscryptd":false,"lastUpdateTime":1767086,"lastWriteDate":{"$date":"2025-06-07T22:36:42Z"},"logicalSessionTimeoutMinutes":30,"maxBsonObjectSize":16777216,"maxMessageSizeBytes":48000000,"maxWireVersion":25,"maxWriteBatchSize":100000,"me":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","roundTripTime":43,"setName":"atlas-l3p1o7-shard-0","setVersion":6,"tags":{"availabilityZone":"afs1-az1","diskState":"READY","nodeType":"ELECTABLE","provider":"AWS","region":"AF_SOUTH_1","workloadType":"OPERATIONAL"},"topologyVersion":{"counter":4,"processId":{"$oid":"6843245a0718701ee5e03836"}},"type":"RSSecondary"},"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017":{"$clusterTime":{"clusterTime":{"$timestamp":{"i":4,"t":**********}},"signature":{"hash":{"0":44,"1":45,"10":208,"11":121,"12":254,"13":94,"14":196,"15":146,"16":99,"17":156,"18":111,"19":196,"2":208,"3":42,"4":248,"5":76,"6":74,"7":113,"8":68,"9":172},"keyId":7464295174923878000}},"address":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":["ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017"],"iscryptd":false,"lastUpdateTime":1767246,"lastWriteDate":{"$date":"2025-06-07T22:36:42Z"},"logicalSessionTimeoutMinutes":30,"maxBsonObjectSize":16777216,"maxMessageSizeBytes":48000000,"maxWireVersion":25,"maxWriteBatchSize":100000,"me":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","roundTripTime":93,"setName":"atlas-l3p1o7-shard-0","setVersion":6,"tags":{"availabilityZone":"afs1-az2","diskState":"READY","nodeType":"ELECTABLE","provider":"AWS","region":"AF_SOUTH_1","workloadType":"OPERATIONAL"},"topologyVersion":{"counter":3,"processId":{"$oid":"68432a416522a5f849a01262"}},"type":"RSSecondary"},"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1766766,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l3p1o7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Server selection timed out after 5000 ms\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":30,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017":{"$clusterTime":{"clusterTime":{"$timestamp":{"i":3,"t":**********}},"signature":{"hash":{"0":44,"1":45,"10":208,"11":121,"12":254,"13":94,"14":196,"15":146,"16":99,"17":156,"18":111,"19":196,"2":208,"3":42,"4":248,"5":76,"6":74,"7":113,"8":68,"9":172},"keyId":7464295174923878000}},"address":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":["ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017"],"iscryptd":false,"lastUpdateTime":1767086,"lastWriteDate":{"$date":"2025-06-07T22:36:42Z"},"logicalSessionTimeoutMinutes":30,"maxBsonObjectSize":16777216,"maxMessageSizeBytes":48000000,"maxWireVersion":25,"maxWriteBatchSize":100000,"me":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","roundTripTime":43,"setName":"atlas-l3p1o7-shard-0","setVersion":6,"tags":{"availabilityZone":"afs1-az1","diskState":"READY","nodeType":"ELECTABLE","provider":"AWS","region":"AF_SOUTH_1","workloadType":"OPERATIONAL"},"topologyVersion":{"counter":4,"processId":{"$oid":"6843245a0718701ee5e03836"}},"type":"RSSecondary"},"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017":{"$clusterTime":{"clusterTime":{"$timestamp":{"i":4,"t":**********}},"signature":{"hash":{"0":44,"1":45,"10":208,"11":121,"12":254,"13":94,"14":196,"15":146,"16":99,"17":156,"18":111,"19":196,"2":208,"3":42,"4":248,"5":76,"6":74,"7":113,"8":68,"9":172},"keyId":7464295174923878000}},"address":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":["ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017"],"iscryptd":false,"lastUpdateTime":1767246,"lastWriteDate":{"$date":"2025-06-07T22:36:42Z"},"logicalSessionTimeoutMinutes":30,"maxBsonObjectSize":16777216,"maxMessageSizeBytes":48000000,"maxWireVersion":25,"maxWriteBatchSize":100000,"me":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","roundTripTime":93,"setName":"atlas-l3p1o7-shard-0","setVersion":6,"tags":{"availabilityZone":"afs1-az2","diskState":"READY","nodeType":"ELECTABLE","provider":"AWS","region":"AF_SOUTH_1","workloadType":"OPERATIONAL"},"topologyVersion":{"counter":3,"processId":{"$oid":"68432a416522a5f849a01262"}},"type":"RSSecondary"},"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":1766766,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l3p1o7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"stack":"MongooseServerSelectionError: Server selection timed out after 5000 ms\n    at _handleConnectionErrors (/home/<USER>/aiinaction/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/aiinaction/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDB (/home/<USER>/aiinaction/backend/src/config/database.js:20:18)\n    at async startServer (/home/<USER>/aiinaction/backend/src/server.js:95:5)","timestamp":"2025-06-08 00:36:45:3645"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError finding by characteristics: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:38:52:3852"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting recommendations: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:38:52:3852"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError searching neighborhoods: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:38:52:3852"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError finding by characteristics: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:39:32:3932"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting recommendations: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:39:32:3932"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError searching neighborhoods: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:198:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 00:39:32:3932"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating market insights: this.shouldUseMockResponses is not a function\u001b[39m","stack":"TypeError: this.shouldUseMockResponses is not a function\n    at GeminiService.generateMarketInsights (/home/<USER>/aiinaction/backend/src/services/geminiService.js:829:16)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:111:42\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:12:45:1245"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating market insights: this.shouldUseMockResponses is not a function\u001b[39m","stack":"TypeError: this.shouldUseMockResponses is not a function\n    at GeminiService.generateMarketInsights (/home/<USER>/aiinaction/backend/src/services/geminiService.js:829:16)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:111:42\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:13:01:131"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating market insights: this.shouldUseMockResponses is not a function\u001b[39m","stack":"TypeError: this.shouldUseMockResponses is not a function\n    at GeminiService.generateMarketInsights (/home/<USER>/aiinaction/backend/src/services/geminiService.js:829:16)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:111:42\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:13:10:1310"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating search explanation: this.shouldUseMockResponses is not a function\u001b[39m","stack":"TypeError: this.shouldUseMockResponses is not a function\n    at GeminiService.generateSearchExplanation (/home/<USER>/aiinaction/backend/src/services/geminiService.js:662:16)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:233:41\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:14:35:1435"}
{"cause":{"beforeHandshake":false,"errorLabelSet":{}},"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching neighborhood: getaddrinfo EAI_AGAIN ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":{"$oid":"7fffffff0000000000000053"},"maxSetVersion":6,"servers":{"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":4010244,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":3999733,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017":{"$clusterTime":null,"address":"ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":4009733,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l3p1o7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"stack":"MongoServerSelectionError: getaddrinfo EAI_AGAIN ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\n    at Topology.selectServer (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/sdam/topology.js:321:38)\n    at async tryOperation (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/operations/execute_operation.js:192:22)\n    at async executeOperation (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)\n    at async FindCursor.next (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/cursor/abstract_cursor.js:347:17)\n    at async Collection.findOne (/home/<USER>/aiinaction/node_modules/mongoose/node_modules/mongodb/lib/collection.js:277:21)\n    at async model.Query._findOne (/home/<USER>/aiinaction/node_modules/mongoose/lib/query.js:2687:15)\n    at async model.Query.exec (/home/<USER>/aiinaction/node_modules/mongoose/lib/query.js:4604:63)","timestamp":"2025-06-08 01:16:45:1645"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError generating insights: this.shouldUseMockResponses is not a function\u001b[39m","stack":"TypeError: this.shouldUseMockResponses is not a function\n    at GeminiService.generateNeighborhoodInsights (/home/<USER>/aiinaction/backend/src/services/geminiService.js:407:16)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:146:42\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:17:25:1725"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching neighborhood: Failed to generate insights\u001b[39m","stack":"Error: Failed to generate insights\n    at GeminiService.generateNeighborhoodInsights (/home/<USER>/aiinaction/backend/src/services/geminiService.js:447:13)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:146:42\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-08 01:17:25:1725"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError finding by characteristics: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:25:025"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting recommendations: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:25:025"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError searching neighborhoods: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:25:025"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError finding by characteristics: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:30:030"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting recommendations: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:30:030"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError searching neighborhoods: No search criteria provided\u001b[39m","stack":"Error: No search criteria provided\n    at VectorSearchService.findByCharacteristics (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:195:15)\n    at VectorSearchService.getRecommendations (/home/<USER>/aiinaction/backend/src/services/vectorSearchService.js:236:40)\n    at /home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:228:55\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at handleValidationErrors (/home/<USER>/aiinaction/backend/src/routes/neighborhoods.js:31:3)\n    at Layer.handle [as handle_request] (/home/<USER>/aiinaction/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/aiinaction/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/aiinaction/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-10 11:00:30:030"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError parsing complex query: entities.lifestyle.map is not a function\u001b[39m","stack":"TypeError: entities.lifestyle.map is not a function\n    at AdvancedNLP.buildSearchCriteria (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:430:58)\n    at AdvancedNLP.parseComplexQuery (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:22:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:47:25","timestamp":"2025-06-10 13:28:54:2854"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting comprehensive neighborhood data: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:28:55:2855"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in revolutionary chat: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:28:55:2855"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError parsing complex query: entities.lifestyle.map is not a function\u001b[39m","stack":"TypeError: entities.lifestyle.map is not a function\n    at AdvancedNLP.buildSearchCriteria (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:430:58)\n    at AdvancedNLP.parseComplexQuery (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:22:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:47:25","timestamp":"2025-06-10 13:30:34:3034"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting comprehensive neighborhood data: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:30:35:3035"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in revolutionary chat: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:30:35:3035"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError parsing complex query: entities.lifestyle.map is not a function\u001b[39m","stack":"TypeError: entities.lifestyle.map is not a function\n    at AdvancedNLP.buildSearchCriteria (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:430:58)\n    at AdvancedNLP.parseComplexQuery (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:22:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:47:25","timestamp":"2025-06-10 13:34:42:3442"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting comprehensive neighborhood data: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:34:42:3442"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in revolutionary chat: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:34:42:3442"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError parsing complex query: entities.lifestyle.map is not a function\u001b[39m","stack":"TypeError: entities.lifestyle.map is not a function\n    at AdvancedNLP.buildSearchCriteria (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:430:58)\n    at AdvancedNLP.parseComplexQuery (/home/<USER>/aiinaction/backend/src/services/advancedNLP.js:22:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:47:25","timestamp":"2025-06-10 13:35:48:3548"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting comprehensive neighborhood data: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:35:48:3548"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in revolutionary chat: Cannot read properties of undefined (reading 'lng')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'lng')\n    at ComprehensiveDataService.getNearbySchools (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:68:39)\n    at ComprehensiveDataService.getComprehensiveNeighborhoodData (/home/<USER>/aiinaction/backend/src/services/comprehensiveDataService.js:37:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/aiinaction/backend/src/routes/chat.js:86:37","timestamp":"2025-06-10 13:35:48:3548"}
