const { MongoClient } = require('mongodb');
const { logger } = require('../utils/logger');

class ComprehensiveDataService {
  constructor() {
    this.client = null;
  }

  async connect() {
    if (!this.client) {
      this.client = new MongoClient(process.env.MONGODB_URI);
      await this.client.connect();
    }
    return this.client;
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
      this.client = null;
    }
  }

  /**
   * Get comprehensive data for a neighborhood including schools, hospitals, and transport
   * @param {Object} neighborhood - Neighborhood data
   * @param {number} schoolRadius - Radius for school search in meters (default: 2000)
   * @param {number} hospitalRadius - Radius for hospital search in meters (default: 5000)
   * @returns {Promise<Object>} - Comprehensive neighborhood data
   */
  async getComprehensiveNeighborhoodData(neighborhood, schoolRadius = 2000, hospitalRadius = 5000) {
    try {
      await this.connect();
      const db = this.client.db();

      const [schools, hospitals, taxiRoutes] = await Promise.all([
        this.getNearbySchools(neighborhood.coordinates, schoolRadius),
        this.getNearbyHospitals(neighborhood.coordinates, hospitalRadius),
        this.getNearbyTaxiRoutes(neighborhood.coordinates)
      ]);

      const analysis = {
        neighborhood,
        education: this.analyzeEducationAccess(schools),
        healthcare: this.analyzeHealthcareAccess(hospitals),
        transport: this.analyzeTransportAccess(taxiRoutes),
        livabilityScore: this.calculateLivabilityScore(schools, hospitals, taxiRoutes, neighborhood)
      };

      return analysis;
    } catch (error) {
      logger.error('Error getting comprehensive neighborhood data:', error);
      throw error;
    }
  }

  /**
   * Get schools near a location
   */
  async getNearbySchools(coordinates, radius = 2000) {
    const collection = this.client.db().collection('pub_schools');
    
    const schools = await collection.find({
      geometry: {
        $near: {
          $geometry: {
            type: "Point",
            coordinates: [coordinates.lng, coordinates.lat]
          },
          $maxDistance: radius
        }
      }
    }).toArray();

    return schools.map(school => ({
      id: school.properties.EMIS,
      name: school.properties.NAME,
      type: school.properties.SCHOOLTYPE,
      district: school.properties.EDUCATIONDISTRICT,
      medium: school.properties.MEDIUMOFINSTRUCTION,
      status: school.properties.SCHOOL_STATUS,
      coordinates: school.geometry.coordinates,
      distance: this.calculateDistance(coordinates, {
        lng: school.geometry.coordinates[0],
        lat: school.geometry.coordinates[1]
      })
    }));
  }

  /**
   * Get hospitals near a location
   */
  async getNearbyHospitals(coordinates, radius = 5000) {
    const collection = this.client.db().collection('pub_hospitals');
    
    const hospitals = await collection.find({
      geometry: {
        $near: {
          $geometry: {
            type: "Point",
            coordinates: [coordinates.lng, coordinates.lat]
          },
          $maxDistance: radius
        }
      }
    }).toArray();

    return hospitals.map(hospital => ({
      id: hospital.properties.OBJECTID,
      name: hospital.properties.NAME,
      classification: hospital.properties.CLASSIFICATION,
      district: hospital.properties.DISTRICT,
      status: hospital.properties.STATUS,
      contact: hospital.properties.TELNO,
      coordinates: hospital.geometry.coordinates,
      distance: this.calculateDistance(coordinates, {
        lng: hospital.geometry.coordinates[0],
        lat: hospital.geometry.coordinates[1]
      })
    }));
  }

  /**
   * Get taxi routes serving an area
   */
  async getNearbyTaxiRoutes(coordinates, radius = 3000) {
    const collection = this.client.db().collection('taxi_routes');
    
    // Find routes that pass near the neighborhood
    const routes = await collection.find({
      geometry: {
        $near: {
          $geometry: {
            type: "Point",
            coordinates: [coordinates.lng, coordinates.lat]
          },
          $maxDistance: radius
        }
      }
    }).limit(20).toArray();

    return routes.map(route => ({
      id: route.properties.OBJECTID,
      origin: route.properties.ORGN,
      destination: route.properties.DSTN,
      length: route.properties.SHAPE_Length
    }));
  }

  /**
   * Analyze education access
   */
  analyzeEducationAccess(schools) {
    const schoolTypes = {};
    const languages = {};
    let operationalCount = 0;

    schools.forEach(school => {
      // Count by type
      schoolTypes[school.type] = (schoolTypes[school.type] || 0) + 1;
      
      // Count by language
      languages[school.medium] = (languages[school.medium] || 0) + 1;
      
      // Count operational schools
      if (school.status === 'Open') operationalCount++;
    });

    const hasFullEducation = schoolTypes['Primary School'] > 0 && schoolTypes['Secondary School'] > 0;
    const educationScore = Math.min(100, (schools.length * 10) + (hasFullEducation ? 20 : 0));

    return {
      totalSchools: schools.length,
      operationalSchools: operationalCount,
      schoolTypes,
      languages,
      hasFullEducation,
      educationScore,
      nearestSchool: schools.length > 0 ? schools[0] : null,
      diversity: Object.keys(languages).length
    };
  }

  /**
   * Analyze healthcare access
   */
  analyzeHealthcareAccess(hospitals) {
    const classifications = {};
    let activeCount = 0;

    hospitals.forEach(hospital => {
      classifications[hospital.classification] = (classifications[hospital.classification] || 0) + 1;
      if (hospital.status === 'Active') activeCount++;
    });

    const hasHospital = classifications['Hospital'] > 0;
    const healthcareScore = Math.min(100, (hospitals.length * 15) + (hasHospital ? 25 : 0));

    return {
      totalFacilities: hospitals.length,
      activeFacilities: activeCount,
      classifications,
      hasHospital,
      healthcareScore,
      nearestFacility: hospitals.length > 0 ? hospitals[0] : null,
      emergencyAccess: hasHospital && hospitals[0]?.distance < 10000 // Within 10km
    };
  }

  /**
   * Analyze transport access
   */
  analyzeTransportAccess(taxiRoutes) {
    const origins = new Set();
    const destinations = new Set();

    taxiRoutes.forEach(route => {
      origins.add(route.origin);
      destinations.add(route.destination);
    });

    const connectivity = origins.size + destinations.size;
    const transportScore = Math.min(100, connectivity * 2);

    return {
      totalRoutes: taxiRoutes.length,
      uniqueOrigins: origins.size,
      uniqueDestinations: destinations.size,
      connectivity,
      transportScore,
      majorDestinations: Array.from(destinations).slice(0, 5)
    };
  }

  /**
   * Calculate overall livability score
   */
  calculateLivabilityScore(schools, hospitals, taxiRoutes, neighborhood) {
    const educationScore = Math.min(100, schools.length * 10);
    const healthcareScore = Math.min(100, hospitals.length * 15);
    const transportScore = Math.min(100, taxiRoutes.length * 5);
    const safetyScore = (neighborhood.safety?.safetyScore || 5) * 10;
    const amenityScore = neighborhood.amenities?.transitScore || 50;

    const weights = {
      education: 0.2,
      healthcare: 0.2,
      transport: 0.15,
      safety: 0.25,
      amenities: 0.2
    };

    const livabilityScore = 
      (educationScore * weights.education) +
      (healthcareScore * weights.healthcare) +
      (transportScore * weights.transport) +
      (safetyScore * weights.safety) +
      (amenityScore * weights.amenities);

    return Math.round(livabilityScore);
  }

  /**
   * Calculate distance between two coordinates (Haversine formula)
   */
  calculateDistance(coord1, coord2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = coord1.lat * Math.PI/180;
    const φ2 = coord2.lat * Math.PI/180;
    const Δφ = (coord2.lat-coord1.lat) * Math.PI/180;
    const Δλ = (coord2.lng-coord1.lng) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return Math.round(R * c); // Distance in meters
  }

  /**
   * Get market insights with comprehensive data
   */
  async getEnhancedMarketInsights() {
    try {
      await this.connect();
      const db = this.client.db();

      const [schoolStats, hospitalStats, neighborhoodStats] = await Promise.all([
        this.getSchoolStatistics(),
        this.getHospitalStatistics(),
        this.getNeighborhoodStatistics()
      ]);

      return {
        education: schoolStats,
        healthcare: hospitalStats,
        neighborhoods: neighborhoodStats,
        insights: this.generateMarketInsights(schoolStats, hospitalStats, neighborhoodStats)
      };
    } catch (error) {
      logger.error('Error getting enhanced market insights:', error);
      throw error;
    }
  }

  async getSchoolStatistics() {
    const collection = this.client.db().collection('pub_schools');
    
    const [total, typeBreakdown, districtBreakdown] = await Promise.all([
      collection.countDocuments(),
      collection.aggregate([
        { $group: { _id: '$properties.SCHOOLTYPE', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]).toArray(),
      collection.aggregate([
        { $group: { _id: '$properties.EDUCATIONDISTRICT', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]).toArray()
    ]);

    return { total, typeBreakdown, districtBreakdown };
  }

  async getHospitalStatistics() {
    const collection = this.client.db().collection('pub_hospitals');
    
    const [total, classificationBreakdown] = await Promise.all([
      collection.countDocuments(),
      collection.aggregate([
        { $group: { _id: '$properties.CLASSIFICATION', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]).toArray()
    ]);

    return { total, classificationBreakdown };
  }

  async getNeighborhoodStatistics() {
    // This would connect to your neighborhoods collection
    // Implementation depends on your neighborhood data structure
    return {
      totalNeighborhoods: 0,
      averageRent: 0,
      safetyDistribution: {}
    };
  }

  generateMarketInsights(schoolStats, hospitalStats, neighborhoodStats) {
    return [
      {
        title: "Education Infrastructure",
        value: `${schoolStats.total} schools`,
        trend: "stable",
        description: `Cape Town has ${schoolStats.total} public schools with ${schoolStats.typeBreakdown[0]?._id} being the most common type.`
      },
      {
        title: "Healthcare Access",
        value: `${hospitalStats.total} facilities`,
        trend: "improving",
        description: `Healthcare infrastructure includes ${hospitalStats.classificationBreakdown.length} different facility types.`
      }
    ];
  }
}

module.exports = ComprehensiveDataService;
