# Agent Integration Guide: Taxi Routes Data

## Overview
This guide explains how to extract and use Cape Town taxi route data from our MongoDB database for integration into external mapping/routing systems.

## Data Structure Understanding

### Document Format
Each taxi route is stored as a separate MongoDB document with this structure:

```json
{
  "_id": "6847d9398d2ed30c6cffc879",
  "type": "Feature",
  "properties": {
    "OBJECTID": 1,
    "ORGN": "BELLVILLE",
    "DSTN": "DURBANVILLE", 
    "SHAPE_Length": 0.*****************
  },
  "geometry": {
    "type": "MultiLineString",
    "coordinates": [
      [
        [18.629837, -33.904826],
        [18.628862, -33.90457],
        [18.62888, -33.904502]
        // ... more coordinate pairs
      ]
    ]
  },
  "importedAt": "2025-06-10T07:05:29.899Z",
  "source": "Taxi_Routes.geojson"
}
```

### Key Fields Explanation

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `_id` | ObjectId | MongoDB unique identifier | Database operations |
| `properties.OBJECTID` | Number | Original route ID from source data | Route identification |
| `properties.ORGN` | String | Origin/starting point name | Route filtering, search |
| `properties.DSTN` | String | Destination/ending point name | Route filtering, search |
| `properties.SHAPE_Length` | Number | Route length in decimal degrees | Distance calculations |
| `geometry.type` | String | Always "MultiLineString" | GIS processing |
| `geometry.coordinates` | Array | GPS coordinates [longitude, latitude] | Map plotting |

## Data Extraction Methods

### Method 1: Direct MongoDB Query
```javascript
const { MongoClient } = require('mongodb');

async function getAllRoutes() {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  
  const collection = client.db().collection('taxi_routes');
  const routes = await collection.find({}).toArray();
  
  await client.close();
  return routes;
}
```

### Method 2: Filtered Extraction
```javascript
async function getRoutesByOrigin(origin) {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  
  const collection = client.db().collection('taxi_routes');
  const routes = await collection.find({
    "properties.ORGN": new RegExp(origin, 'i')
  }).toArray();
  
  await client.close();
  return routes;
}
```

### Method 3: Geospatial Query
```javascript
async function getRoutesNearLocation(longitude, latitude, maxDistanceMeters) {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  
  const collection = client.db().collection('taxi_routes');
  const routes = await collection.find({
    "geometry": {
      $near: {
        $geometry: {
          type: "Point",
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistanceMeters
      }
    }
  }).toArray();
  
  await client.close();
  return routes;
}
```

## Data Transformation for External Systems

### For Google Maps API
```javascript
function transformForGoogleMaps(mongoRoute) {
  return {
    routeId: mongoRoute.properties.OBJECTID,
    origin: mongoRoute.properties.ORGN,
    destination: mongoRoute.properties.DSTN,
    path: mongoRoute.geometry.coordinates[0].map(coord => ({
      lat: coord[1],  // Note: MongoDB stores [lng, lat], Google wants {lat, lng}
      lng: coord[0]
    })),
    distance: mongoRoute.properties.SHAPE_Length,
    strokeColor: '#FF0000',
    strokeWeight: 3
  };
}
```

### For Leaflet.js
```javascript
function transformForLeaflet(mongoRoute) {
  return {
    type: 'Feature',
    properties: {
      routeId: mongoRoute.properties.OBJECTID,
      origin: mongoRoute.properties.ORGN,
      destination: mongoRoute.properties.DSTN,
      distance: mongoRoute.properties.SHAPE_Length,
      popupContent: `${mongoRoute.properties.ORGN} → ${mongoRoute.properties.DSTN}`
    },
    geometry: {
      type: 'LineString',
      coordinates: mongoRoute.geometry.coordinates[0] // [lng, lat] format is correct for Leaflet
    }
  };
}
```

### For Mapbox
```javascript
function transformForMapbox(mongoRoute) {
  return {
    type: 'Feature',
    properties: {
      'route-id': mongoRoute.properties.OBJECTID,
      'origin': mongoRoute.properties.ORGN,
      'destination': mongoRoute.properties.DSTN,
      'distance': mongoRoute.properties.SHAPE_Length
    },
    geometry: {
      type: 'LineString',
      coordinates: mongoRoute.geometry.coordinates[0]
    }
  };
}
```

## Complete Integration Example

### Express.js API Endpoint
```javascript
const express = require('express');
const { MongoClient } = require('mongodb');
const app = express();

// Get all routes
app.get('/api/routes', async (req, res) => {
  try {
    const client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    
    const collection = client.db().collection('taxi_routes');
    const routes = await collection.find({}).toArray();
    
    // Transform for your mapping system
    const transformedRoutes = routes.map(route => ({
      id: route.properties.OBJECTID,
      origin: route.properties.ORGN,
      destination: route.properties.DSTN,
      coordinates: route.geometry.coordinates[0],
      length: route.properties.SHAPE_Length
    }));
    
    await client.close();
    res.json(transformedRoutes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get routes by origin
app.get('/api/routes/from/:origin', async (req, res) => {
  try {
    const client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    
    const collection = client.db().collection('taxi_routes');
    const routes = await collection.find({
      "properties.ORGN": new RegExp(req.params.origin, 'i')
    }).toArray();
    
    await client.close();
    res.json(routes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## Important Coordinate Notes

### Coordinate Format
- **MongoDB Storage**: `[longitude, latitude]` (GeoJSON standard)
- **Google Maps**: `{lat: latitude, lng: longitude}`
- **Leaflet**: `[longitude, latitude]` (same as MongoDB)
- **Mapbox**: `[longitude, latitude]` (same as MongoDB)

### Cape Town Coordinate Ranges
- **Longitude**: ~18.3 to 18.9 (East)
- **Latitude**: ~-34.4 to -33.7 (South - negative values)

## Performance Considerations

### Batch Processing
```javascript
async function getRoutesInBatches(batchSize = 100) {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  
  const collection = client.db().collection('taxi_routes');
  const cursor = collection.find({}).batchSize(batchSize);
  
  const batches = [];
  let batch = [];
  
  await cursor.forEach(doc => {
    batch.push(doc);
    if (batch.length === batchSize) {
      batches.push([...batch]);
      batch = [];
    }
  });
  
  if (batch.length > 0) {
    batches.push(batch);
  }
  
  await client.close();
  return batches;
}
```

### Projection for Large Datasets
```javascript
// Only get essential fields for mapping
const routes = await collection.find({}, {
  projection: {
    'properties.OBJECTID': 1,
    'properties.ORGN': 1,
    'properties.DSTN': 1,
    'geometry.coordinates': 1
  }
}).toArray();
```

## Common Integration Patterns

### 1. Real-time Route Display
- Query routes by origin/destination
- Transform coordinates for your mapping library
- Display as polylines/paths on map

### 2. Route Search/Autocomplete
- Use text search on origin/destination fields
- Implement fuzzy matching for user input
- Return matching route options

### 3. Geospatial Analysis
- Find routes within geographic bounds
- Calculate route intersections
- Analyze route density by area

### 4. Route Planning
- Find connecting routes between locations
- Calculate multi-hop journeys
- Optimize route selection

## Error Handling
```javascript
try {
  const routes = await getRoutes();
  // Process routes
} catch (error) {
  console.error('Database error:', error);
  // Handle connection issues, invalid queries, etc.
}
```

## Testing Your Integration
1. Start with a small subset of routes (limit: 10)
2. Verify coordinate transformation is correct
3. Test with different origins/destinations
4. Validate map rendering performance
5. Test error scenarios (no routes found, invalid coordinates)

## Quick Start for Agents

### Option 1: Use the Ready-Made API
```bash
# Install dependencies
npm install express cors

# Start the API server
node taxi_routes_api.js

# Access data via HTTP endpoints
curl "http://localhost:3000/api/routes?origin=bellville&limit=10"
```

### Option 2: Use the Export Tool
```bash
# Export data for your specific mapping system
node data_export_examples.js --google-maps
node data_export_examples.js --leaflet
node data_export_examples.js --mapbox
```

### Option 3: Direct Database Access
```javascript
const { MongoClient } = require('mongodb');
// Use the code examples above for direct MongoDB queries
```

## API Endpoints Summary

| Endpoint | Purpose | Example |
|----------|---------|---------|
| `/api/routes` | Get filtered routes | `?origin=bellville&limit=10` |
| `/api/routes/geojson` | GeoJSON for Leaflet/Mapbox | `?destination=cape%20town` |
| `/api/routes/googlemaps` | Google Maps format | `?minLength=0.2` |
| `/api/routes/:id` | Single route details | `/api/routes/1` |
| `/api/origins` | All origin locations | - |
| `/api/destinations` | All destination locations | - |
| `/api/routes/near/:lng/:lat` | Geospatial search | `/near/18.6/-33.9?maxDistance=1000` |
| `/api/stats` | Route statistics | - |
| `/api/search/:term` | Text search | `/search/town` |

## Data Format Examples

### Raw MongoDB Document
```json
{
  "properties": {
    "OBJECTID": 1,
    "ORGN": "BELLVILLE",
    "DSTN": "DURBANVILLE",
    "SHAPE_Length": 0.100633
  },
  "geometry": {
    "coordinates": [[[18.629837, -33.904826], [18.628862, -33.90457]]]
  }
}
```

### Google Maps Format
```json
{
  "routeId": 1,
  "origin": "BELLVILLE",
  "destination": "DURBANVILLE",
  "path": [{"lat": -33.904826, "lng": 18.629837}],
  "strokeColor": "#00ff00"
}
```

### GeoJSON Format (Leaflet/Mapbox)
```json
{
  "type": "Feature",
  "properties": {"routeId": 1, "origin": "BELLVILLE"},
  "geometry": {
    "type": "LineString",
    "coordinates": [[18.629837, -33.904826]]
  }
}
```

This data structure provides comprehensive taxi route information for Cape Town, ready for integration into any mapping or routing system.
