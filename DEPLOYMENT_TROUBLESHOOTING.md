# Deployment Troubleshooting Guide

## Cloud Run Job Deployment Issues

### Problem 1: Job Not Found Error
When running the Cloud Build pipeline, you encounter this error:
```
ERROR: (gcloud.run.jobs.execute) NOT_FOUND: Resource 'city-insights-data-import' of kind 'JOB' in region 'us-central1' in project 'your-project-id' does not exist.
```

### Problem 2: YAML Parsing Error
When trying to deploy using YAML configuration, you encounter:
```
ERROR: (gcloud.run.jobs.replace) Failed to parse value(s) in protobuf [Job]:
  Job.spec.template.spec.template.spec.{parallelism,restartPolicy,taskCount,taskTimeoutSeconds}
```

### Root Cause
1. The Cloud Build configuration tries to execute a Cloud Run job that hasn't been created yet
2. The Cloud Run Jobs YAML schema can be complex and error-prone

### Solution Approach
The updated solution uses `gcloud` commands instead of YAML files for more reliable deployment.

### Solution

#### Option 1: Use the Updated Cloud Build Pipeline (Recommended)
The updated `cloudbuild.yaml` now includes steps to build and deploy the data import job before executing it:

1. **Builds the data import job Docker image** using `backend/Dockerfile.job`
2. **Pushes the image** to Google Container Registry
3. **Deploys the job** using the `job.yaml` configuration
4. **Executes the job** for data import

Simply trigger your Cloud Build pipeline and it will handle everything automatically.

#### Option 2: Manual Deployment
If you want to deploy the job manually first:

1. **Set your project ID:**
   ```bash
   export GOOGLE_CLOUD_PROJECT_ID=your-project-id
   ```

2. **Run the deployment script:**
   ```bash
   ./scripts/deploy-job.sh
   ```

3. **Or deploy manually:**
   ```bash
   # Build and push the image
   cd backend
   docker build -f Dockerfile.job -t gcr.io/$GOOGLE_CLOUD_PROJECT_ID/city-insights-data-import:latest .
   docker push gcr.io/$GOOGLE_CLOUD_PROJECT_ID/city-insights-data-import:latest
   cd ..

   # Deploy the job (create or update)
   gcloud run jobs create city-insights-data-import \
     --image gcr.io/$GOOGLE_CLOUD_PROJECT_ID/city-insights-data-import:latest \
     --region=us-central1 \
     --task-timeout 3600 \
     --parallelism 1 \
     --task-count 1 \
     --max-retries 3 \
     --memory 4Gi \
     --cpu 2 \
     --set-env-vars NODE_ENV=production,GOOGLE_CLOUD_PROJECT_ID=$GOOGLE_CLOUD_PROJECT_ID \
     --set-secrets MONGODB_URI=mongodb-uri:latest,MONGODB_DB_NAME=mongodb-db-name:latest,GEMINI_API_KEY=gemini-api-key:latest
   ```

#### Option 3: Skip the Data Import Step
If you don't need the data import job to run during deployment, you can comment out or remove the `run-migrations` step from `cloudbuild.yaml`:

```yaml
# Comment out or remove this step:
# - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
#   entrypoint: 'gcloud'
#   args:
#     - 'run'
#     - 'jobs'
#     - 'execute'
#     - 'city-insights-data-import'
#     - '--region'
#     - 'us-central1'
#     - '--wait'
#   id: 'run-migrations'
#   waitFor: ['deploy-backend', 'deploy-data-import-job']
```

### Files Created/Modified

1. **`job.yaml`** - Cloud Run job configuration
2. **`backend/Dockerfile.job`** - Specialized Dockerfile for the data import job
3. **`scripts/deploy-job.sh`** - Manual deployment script
4. **`cloudbuild.yaml`** - Updated to include job deployment steps

### Verification

After deployment, you can verify the job exists:

```bash
gcloud run jobs list --region=us-central1
```

And execute it manually:

```bash
gcloud run jobs execute city-insights-data-import --region=us-central1 --wait
```

### Environment Variables Required

The data import job requires these environment variables (configured via Google Cloud Secret Manager):

- `MONGODB_URI` - MongoDB connection string
- `MONGODB_DB_NAME` - Database name
- `GEMINI_API_KEY` - Google AI API key
- `GOOGLE_CLOUD_PROJECT_ID` - Your Google Cloud project ID

Make sure these secrets are created in Google Cloud Secret Manager before deploying.
