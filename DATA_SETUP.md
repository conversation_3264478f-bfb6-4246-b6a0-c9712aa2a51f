# 📊 Data Setup Guide for City Insights AI

This guide explains how to populate your MongoDB database with neighborhood data for the City Insights AI application.

## 🚀 Quick Start (Recommended)

For immediate testing and development, use the sample data:

```bash
# Import sample neighborhood data
npm run data:import

# Set up database indexes
npm run setup:vector

# Generate AI embeddings (requires Google Cloud setup)
npm run setup:embeddings

# Or run everything at once
npm run setup:complete
```

## 📋 Available Data Import Methods

### 1. **Sample Data Import (Fastest)**
```bash
npm run data:import
```
- Imports 15 pre-configured NYC neighborhoods
- Includes realistic metrics and descriptions
- Perfect for development and testing
- Takes ~30 seconds

### 2. **CSV Import**
```bash
# Create sample CSV file
cd backend && node scripts/csvImport.js create-csv

# Import from CSV
cd backend && node scripts/csvImport.js import ./data/sample_neighborhoods.csv

# Or use your own CSV file
cd backend && node scripts/csvImport.js import /path/to/your/neighborhoods.csv
```

### 3. **NYC Open Data API (Full Dataset)**
```bash
# Quick setup with processed data
npm run data:ingest

# Full ingestion from NYC APIs (requires API key)
npm run data:ingest:full
```

### 4. **Manual Database Setup**
If you prefer to set up data manually, see the [Manual Setup](#manual-setup) section below.

## 📁 CSV Format

If you want to import your own data, create a CSV file with these columns:

### Required Columns:
- `name` - Neighborhood name
- `borough` - Borough (Manhattan, Brooklyn, Queens, Bronx, Staten Island)
- `lat` - Latitude coordinate
- `lng` - Longitude coordinate

### Optional Columns:
- `population` - Population count
- `median_age` - Median age of residents
- `median_income` - Median household income
- `avg_rent` - Average monthly rent
- `safety_score` - Safety score (0-10)
- `transit_score` - Transit accessibility (0-100)
- `walkability_score` - Walkability score (0-100)
- `restaurants` - Number of restaurants
- `schools` - Number of schools
- `parks` - Number of parks
- `description` - Neighborhood description
- `tags` - Comma-separated tags (e.g., "trendy,expensive,artistic")

### Example CSV:
```csv
name,borough,lat,lng,population,median_age,median_income,avg_rent,safety_score,transit_score,walkability_score,restaurants,schools,parks,description,tags
SoHo,Manhattan,40.7230,-74.0030,15000,35,120000,5500,8.5,95,98,150,8,3,"Trendy neighborhood known for cast-iron architecture","trendy,expensive,artistic"
```

## 🗄️ Database Structure

The application creates these MongoDB collections:

### `neighborhoods` Collection:
```javascript
{
  name: "SoHo",
  borough: "Manhattan",
  coordinates: { lat: 40.7230, lng: -74.0030 },
  demographics: {
    population: 15000,
    medianAge: 35,
    medianIncome: 120000,
    educationLevel: { highSchool: 95, bachelors: 75, graduate: 45 },
    ethnicComposition: { white: 70, black: 8, hispanic: 12, asian: 8, other: 2 }
  },
  housing: {
    avgRent: 5500,
    avgSalePrice: 2500000,
    rentalAvailability: 15,
    homeOwnershipRate: 25,
    pricePerSqFt: 1800
  },
  safety: {
    crimeRate: 2.1,
    safetyScore: 8.5,
    crimeTypes: { violent: 5, property: 15, drug: 3, other: 7 }
  },
  amenities: {
    restaurants: 150,
    schools: 8,
    parks: 3,
    transitScore: 95,
    walkabilityScore: 98,
    bikeScore: 85,
    groceryStores: 25,
    hospitals: 2
  },
  description: "Trendy neighborhood known for cast-iron architecture...",
  tags: ["trendy", "expensive", "artistic", "shopping", "nightlife"],
  vectorEmbedding: [0.1, 0.2, ...], // 768-dimensional vector for similarity search
  lastUpdated: "2024-01-15T10:30:00Z"
}
```

### `crimedatas` Collection:
```javascript
{
  neighborhood: "SoHo",
  borough: "Manhattan",
  incidentType: "THEFT",
  category: "PROPERTY",
  date: "2024-01-10T14:30:00Z",
  coordinates: { lat: 40.7231, lng: -74.0031 },
  severity: "MEDIUM",
  timeOfDay: "AFTERNOON",
  dayOfWeek: "WEDNESDAY",
  month: 1,
  year: 2024,
  resolved: true
}
```

## 🔧 Manual Setup

If you prefer to set up the database manually:

### 1. Connect to MongoDB
```javascript
// Using MongoDB Compass or mongo shell
use cityinsights
```

### 2. Create Collections
```javascript
db.createCollection("neighborhoods")
db.createCollection("crimedatas")
```

### 3. Create Indexes
```javascript
// Neighborhood indexes
db.neighborhoods.createIndex({ "name": 1, "borough": 1 }, { unique: true })
db.neighborhoods.createIndex({ "coordinates": "2dsphere" })
db.neighborhoods.createIndex({ "housing.avgRent": 1 })
db.neighborhoods.createIndex({ "safety.safetyScore": -1 })
db.neighborhoods.createIndex({ "amenities.transitScore": -1 })

// Crime data indexes
db.crimedatas.createIndex({ "neighborhood": 1, "date": -1 })
db.crimedatas.createIndex({ "coordinates": "2dsphere" })
db.crimedatas.createIndex({ "year": 1, "month": 1 })
```

### 4. Insert Sample Data
```javascript
db.neighborhoods.insertOne({
  name: "SoHo",
  borough: "Manhattan",
  coordinates: { lat: 40.7230, lng: -74.0030 },
  // ... rest of the data structure
})
```

## 🤖 Vector Search Setup

For AI-powered similarity search, you need to:

### 1. Create Vector Search Index in MongoDB Atlas
1. Go to your MongoDB Atlas cluster
2. Navigate to "Search" → "Create Search Index"
3. Choose "JSON Editor"
4. Use this configuration:

```json
{
  "fields": [
    {
      "type": "vector",
      "path": "vectorEmbedding",
      "numDimensions": 768,
      "similarity": "cosine"
    }
  ]
}
```

5. Name it: `neighborhood_vector_index`
6. Select the `neighborhoods` collection

### 2. Generate Embeddings
```bash
# Generate embeddings for all neighborhoods
npm run setup:embeddings
```

**Note:** This requires Google Cloud Vertex AI setup with valid credentials.

## 🌐 NYC Open Data Integration

To use real NYC data, you can optionally get an API key:

1. Visit [NYC Open Data](https://opendata.cityofnewyork.us/)
2. Create an account and get an API key
3. Add to your `.env` file:
   ```env
   NYC_OPEN_DATA_API_KEY=your-api-key-here
   ```

### Available NYC Datasets:
- **Housing**: Housing New York Units by Building
- **Crime**: NYPD Complaint Data Current
- **311 Requests**: Quality of life indicators
- **Demographics**: American Community Survey data

## 🔍 Verification

After importing data, verify everything is working:

```bash
# Check if data was imported
mongosh "your-connection-string" --eval "db.neighborhoods.countDocuments()"

# Check if indexes exist
mongosh "your-connection-string" --eval "db.neighborhoods.getIndexes()"

# Test the API
curl http://localhost:8080/api/neighborhoods
```

## 🚨 Troubleshooting

### Common Issues:

1. **"Collection not found"**
   - Run the import scripts first
   - Check MongoDB connection string

2. **"Vector search not working"**
   - Ensure vector index is created in Atlas
   - Check if embeddings are generated
   - Verify Google Cloud credentials

3. **"No neighborhoods returned"**
   - Check if data import completed successfully
   - Verify database name in connection string

4. **"API key errors"**
   - NYC Open Data API key is optional for basic functionality
   - Google Cloud credentials are required for embeddings

### Getting Help:

1. Check the logs: `tail -f backend/logs/combined.log`
2. Verify environment variables are set correctly
3. Test database connection independently
4. Check if all required services are running

## 📈 Performance Tips

- **For Development**: Use sample data (fastest)
- **For Production**: Use full NYC data ingestion
- **For Custom Areas**: Create your own CSV files
- **For Large Datasets**: Import in batches and use indexes

## 🎯 Next Steps

After setting up data:

1. **Start the application**: `npm run dev`
2. **Test the API**: Visit `http://localhost:8080/api/neighborhoods`
3. **Use the frontend**: Visit `http://localhost:5173`
4. **Try AI chat**: Ask questions about neighborhoods
5. **Explore maps**: View neighborhoods on the interactive map

Your City Insights AI application is now ready with real neighborhood data! 🎉
